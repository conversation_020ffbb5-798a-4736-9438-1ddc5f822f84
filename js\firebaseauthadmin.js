// Import Firebase modules
import { initializeApp } from "https://www.gstatic.com/firebasejs/10.11.1/firebase-app.js";
import { getAuth, createUserWithEmailAndPassword, signInWithEmailAndPassword, signOut } from "https://www.gstatic.com/firebasejs/10.11.1/firebase-auth.js";
import { getFirestore, setDoc, doc, getDoc } from "https://www.gstatic.com/firebasejs/10.11.1/firebase-firestore.js";

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyBuj8sMvbDKmjkAVG5JdVOdEF4OO9ijjzA",
  authDomain: "lead-login.firebaseapp.com",
  projectId: "lead-login",
  storageBucket: "lead-login.appspot.com",
  messagingSenderId: "1051456252675",
  appId: "1:1051456252675:web:61073e11903055f889d736"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const auth = getAuth(app);
const db = getFirestore(app);

// Login attempt tracking
let failedLoginAttempts = 0;
const maxFailedAttempts = 10;
const disableDuration = 3 * 60 * 1000; // 3 minutes

// Display messages
function showMessage(message, divId) {
  const messageDiv = document.getElementById(divId);
  messageDiv.style.display = "block";
  messageDiv.innerHTML = message;
  messageDiv.style.opacity = 1;
  setTimeout(() => {
    messageDiv.style.opacity = 0;
  }, 5000);
}

// Signup event
document.getElementById('submitSignUp').addEventListener('click', async (event) => {
  event.preventDefault();
  const email = document.getElementById('rEmail').value;
  const password = document.getElementById('rPassword').value;
  const firstName = document.getElementById('fName').value;
  const lastName = document.getElementById('lName').value;

  try {
    const userCredential = await createUserWithEmailAndPassword(auth, email, password);
    const user = userCredential.user;

    // Store user data in Firestore under "Admins"
    const userData = { email, firstName, lastName };
    await setDoc(doc(db, "Admins", user.uid), userData);

    showMessage('Account Created Successfully', 'signUpMessage');
    localStorage.removeItem('showSignUp');
    document.getElementById('signin').style.display = 'none';
  } catch (error) {
    if (error.code === 'auth/email-already-in-use') {
      showMessage('Email Address Already Exists', 'signUpMessage');
    } else {
      showMessage('Unable to create User', 'signUpMessage');
    }
  }
});

// Sign-in event
const signInButton = document.getElementById('submitSignIn');
signInButton.addEventListener('click', async (event) => {
  event.preventDefault();
  const email = document.getElementById('email').value;
  const password = document.getElementById('password').value;

  try {
    const userCredential = await signInWithEmailAndPassword(auth, email, password);
    const user = userCredential.user;

    // DEBUG LOG: Checking User ID
    console.log("User ID:", user.uid);

    // Check if user is in "Admins"
    const adminRef = doc(db, "Admins", user.uid);
    const adminSnap = await getDoc(adminRef);
    
    if (!adminSnap.exists()) {
      console.log("User is NOT an Admin");
      showMessage('Access denied', 'signInMessage');
      await signOut(auth);
      return;
    }
    console.log("User is an Admin, proceeding...");

    // Check if user is in "Instructors"
    const instructorRef = doc(db, "Instructors", user.uid);
    const instructorSnap = await getDoc(instructorRef);

    if (instructorSnap.exists()) {
      console.log("User is an Instructor, access denied.");
      showMessage('Access Denied', 'signInMessage');
      await signOut(auth);
      return;
    }

    // Get admin data and store in localStorage with session timestamp
    const adminData = adminSnap.data();
    adminData.sessionTimestamp = Date.now(); // Add session timestamp for security
    localStorage.setItem('adminData', JSON.stringify(adminData));
    localStorage.setItem('loggedInUserId', user.uid);

    // Successful login
    console.log("User login successful.");
    showMessage('Login Successful', 'signInMessage');
    window.location.href = 'Admin.html';

  } catch (error) {
    failedLoginAttempts++;
    console.error("Login Error:", error.code, error.message);

    if (error.code === 'auth/user-not-found') {
      showMessage('Account does not exist.', 'signInMessage');
    } else if (error.code === 'auth/wrong-password') {
      showMessage('Incorrect Email or Password', 'signInMessage');
    } else {
      showMessage('Unable to log in', 'signInMessage');
    }

    // Lockout logic
    if (failedLoginAttempts >= maxFailedAttempts) {
      signInButton.disabled = true;
      const countdownMessage = document.getElementById('countdownMessage');
      countdownMessage.style.display = 'block';

      let timeLeft = disableDuration / 1000; // Convert to seconds
      const countdownInterval = setInterval(() => {
        timeLeft--;
        countdownMessage.textContent = `Please wait ${timeLeft} seconds before trying again.`;

        if (timeLeft <= 0) {
          clearInterval(countdownInterval);
          countdownMessage.style.display = 'none';
          signInButton.disabled = false;
          failedLoginAttempts = 0; // Reset after lockout period
        }
      }, 1000);
    }
  }
});
