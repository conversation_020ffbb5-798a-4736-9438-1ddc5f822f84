/**
 * Session Security Management System
 * Provides secure session validation and management for all user types
 */

class SessionSecurity {
    constructor() {
        this.SESSION_TIMEOUT = 30 * 60 * 1000; // 30 minutes
        this.sessionTimer = null;
        this.isLoggingOut = false;
        
        // Bind methods to preserve context
        this.validateSession = this.validateSession.bind(this);
        this.handleSessionTimeout = this.handleSessionTimeout.bind(this);
        this.resetSessionTimer = this.resetSessionTimer.bind(this);
        this.clearSession = this.clearSession.bind(this);
        this.setupBeforeUnloadHandler = this.setupBeforeUnloadHandler.bind(this);
    }

    /**
     * Validates if the current session is valid
     * @param {string} userType - 'student', 'teacher', or 'admin'
     * @returns {boolean} - true if session is valid, false otherwise
     */
    validateSession(userType) {
        try {
            const storageKey = `${userType}Data`;
            const userData = localStorage.getItem(storageKey);
            
            if (!userData) {
                console.log(`No ${userType} data found in localStorage`);
                return false;
            }

            const parsedData = JSON.parse(userData);
            
            // Check if session has required fields
            if (!parsedData.email || !parsedData.firstName || !parsedData.lastName) {
                console.log(`Invalid ${userType} data structure`);
                return false;
            }

            // Check session timestamp if it exists
            if (parsedData.sessionTimestamp) {
                const currentTime = Date.now();
                const sessionAge = currentTime - parsedData.sessionTimestamp;
                
                if (sessionAge > this.SESSION_TIMEOUT) {
                    console.log(`${userType} session has expired`);
                    this.clearSession(userType);
                    return false;
                }
            } else {
                // Add timestamp to existing sessions for future validation
                parsedData.sessionTimestamp = Date.now();
                localStorage.setItem(storageKey, JSON.stringify(parsedData));
            }

            // Session is valid, reset the timer
            this.resetSessionTimer(userType);
            return true;

        } catch (error) {
            console.error(`Error validating ${userType} session:`, error);
            return false;
        }
    }

    /**
     * Updates session timestamp to extend session
     * @param {string} userType - 'student', 'teacher', or 'admin'
     */
    updateSessionTimestamp(userType) {
        try {
            const storageKey = `${userType}Data`;
            const userData = localStorage.getItem(storageKey);
            
            if (userData) {
                const parsedData = JSON.parse(userData);
                parsedData.sessionTimestamp = Date.now();
                localStorage.setItem(storageKey, JSON.stringify(parsedData));
            }
        } catch (error) {
            console.error(`Error updating ${userType} session timestamp:`, error);
        }
    }

    /**
     * Resets the session timeout timer
     * @param {string} userType - 'student', 'teacher', or 'admin'
     */
    resetSessionTimer(userType) {
        if (this.sessionTimer) {
            clearTimeout(this.sessionTimer);
        }
        
        this.sessionTimer = setTimeout(() => {
            console.log(`${userType} session timed out due to inactivity`);
            this.handleSessionTimeout(userType);
        }, this.SESSION_TIMEOUT);
        
        // Update session timestamp
        this.updateSessionTimestamp(userType);
    }

    /**
     * Handles session timeout
     * @param {string} userType - 'student', 'teacher', or 'admin'
     */
    async handleSessionTimeout(userType) {
        try {
            this.clearSession(userType);
            alert('Your session has expired due to inactivity. Please log in again.');
            window.location.href = 'index.html';
        } catch (error) {
            console.error(`Error during ${userType} session timeout:`, error);
            // Force redirect even if there's an error
            window.location.href = 'index.html';
        }
    }

    /**
     * Clears session data for a specific user type
     * @param {string} userType - 'student', 'teacher', or 'admin'
     */
    clearSession(userType) {
        try {
            const storageKey = `${userType}Data`;
            localStorage.removeItem(storageKey);
            localStorage.removeItem('loggedInUserId');
            
            // Clear any user-specific data
            if (userType === 'student') {
                localStorage.removeItem('enrolledClassrooms');
            } else if (userType === 'teacher') {
                localStorage.removeItem('teacherCards');
            }
            
            if (this.sessionTimer) {
                clearTimeout(this.sessionTimer);
                this.sessionTimer = null;
            }
        } catch (error) {
            console.error(`Error clearing ${userType} session:`, error);
        }
    }

    /**
     * Performs logout with proper cleanup
     * @param {string} userType - 'student', 'teacher', or 'admin'
     */
    async performLogout(userType) {
        try {
            this.isLoggingOut = true;
            this.clearSession(userType);
            
            // If Firebase auth is available, sign out
            if (window.auth && typeof window.auth.signOut === 'function') {
                await window.auth.signOut();
            }
            
            window.location.href = 'index.html';
        } catch (error) {
            console.error(`Error during ${userType} logout:`, error);
            // Force redirect even if there's an error
            this.clearSession(userType);
            window.location.href = 'index.html';
        }
    }

    /**
     * Sets up beforeunload handler to clear session when browser/tab is closed
     * @param {string} userType - 'student', 'teacher', or 'admin'
     */
    setupBeforeUnloadHandler(userType) {
        // Handle beforeunload event (when user closes browser/tab)
        window.addEventListener('beforeunload', (event) => {
            // Only clear session if not logging out intentionally
            if (!this.isLoggingOut) {
                console.log(`Browser/tab closing, clearing ${userType} session`);
                this.clearSession(userType);
            }
        });

        // Handle unload event as a backup
        window.addEventListener('unload', (event) => {
            if (!this.isLoggingOut) {
                console.log(`Page unloading, clearing ${userType} session`);
                this.clearSession(userType);
            }
        });

        // Handle page visibility changes (when tab becomes hidden)
        document.addEventListener('visibilitychange', () => {
            if (document.visibilityState === 'hidden' && !this.isLoggingOut) {
                // Update timestamp when tab becomes hidden
                this.updateSessionTimestamp(userType);
            } else if (document.visibilityState === 'visible') {
                // Validate session when tab becomes visible again
                if (!this.validateSession(userType)) {
                    window.location.href = 'index.html';
                }
            }
        });

        // Handle pagehide event (more reliable than unload in some browsers)
        window.addEventListener('pagehide', (event) => {
            if (!this.isLoggingOut) {
                console.log(`Page hiding, clearing ${userType} session`);
                this.clearSession(userType);
            }
        });
    }

    /**
     * Initializes session security for a specific user type
     * @param {string} userType - 'student', 'teacher', or 'admin'
     * @param {string} redirectPage - page to redirect to if session is invalid
     * @returns {boolean} - true if session is valid and initialized
     */
    initializeSession(userType, redirectPage = 'index.html') {
        // Validate session first
        if (!this.validateSession(userType)) {
            console.log(`Invalid ${userType} session, redirecting to ${redirectPage}`);
            window.location.href = redirectPage;
            return false;
        }

        // Set up session management
        this.setupBeforeUnloadHandler(userType);
        
        // Set up activity listeners to reset session timer
        const activityEvents = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
        activityEvents.forEach(event => {
            document.addEventListener(event, () => {
                this.resetSessionTimer(userType);
            }, { passive: true });
        });

        console.log(`${userType} session initialized successfully`);
        return true;
    }
}

// Create global instance
window.SessionSecurity = SessionSecurity;

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SessionSecurity;
}
