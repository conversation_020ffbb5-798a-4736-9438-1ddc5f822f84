// Import Firebase modules
import { initializeApp } from "https://www.gstatic.com/firebasejs/10.11.1/firebase-app.js";
import { getAuth, createUserWithEmailAndPassword, signInWithEmailAndPassword } from "https://www.gstatic.com/firebasejs/10.11.1/firebase-auth.js";
import { getFirestore, setDoc, doc, collection, getDoc } from "https://www.gstatic.com/firebasejs/10.11.1/firebase-firestore.js";

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyBuj8sMvbDKmjkAVG5JdVOdEF4OO9ijjzA",
  authDomain: "lead-login.firebaseapp.com",
  projectId: "lead-login",
  storageBucket: "lead-login.appspot.com",
  messagingSenderId: "1051456252675",
  appId: "1:1051456252675:web:61073e11903055f889d736"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const auth = getAuth(app);
const db = getFirestore(app);

// Login attempt tracking
let failedLoginAttempts = 0;
const maxFailedAttempts = 10;
const disableDuration = 3 * 60 * 1000; // 3 minutes

// Display messages
function showMessage(message, divId) {
  const messageDiv = document.getElementById(divId);
  messageDiv.style.display = "block";
  messageDiv.innerHTML = message;
  messageDiv.style.opacity = 1;
  setTimeout(() => {
    messageDiv.style.opacity = 0;
  }, 5000);
}

// Signup event
const signUp = document.getElementById('submitSignUp');
signUp.addEventListener('click', async (event) => {
  event.preventDefault();
  const email = document.getElementById('rEmail').value;
  const password = document.getElementById('rPassword').value;
  const firstName = document.getElementById('fName').value;
  const lastName = document.getElementById('lName').value;

  try {
    const userCredential = await createUserWithEmailAndPassword(auth, email, password);
    const user = userCredential.user;

    // Store user data in Firestore with verification status
    const userData = {
      email,
      firstName,
      lastName,
      verificationStatus: 'pending', // Add verification status field
      createdAt: new Date().toISOString() // Add timestamp for sorting
    };
    await setDoc(doc(db, "Instructors", user.uid), userData);

    showMessage('Account Created Successfully. Please wait for admin approval before logging in.', 'signUpMessage');
    localStorage.removeItem('showSignUp');
    document.getElementById('signin').style.display = 'none';
  } catch (error) {
    if (error.code === 'auth/email-already-in-use') {
      showMessage('Email Address Already Exists', 'signUpMessage');
    } else {
      showMessage('Unable to create User', 'signUpMessage');
    }
  }
});

// Sign-in event
const signInButton = document.getElementById('submitSignIn');
signInButton.addEventListener('click', async (event) => {
  event.preventDefault();
  const email = document.getElementById('email').value;
  const password = document.getElementById('password').value;

  try {
    const userCredential = await signInWithEmailAndPassword(auth, email, password);
    const user = userCredential.user;

    // Check if user exists in the "Students" collection
    const studentDocRef = doc(db, "Students", user.uid);
    const studentDoc = await getDoc(studentDocRef);

    if (studentDoc.exists()) {
      showMessage('Access denied.', 'signInMessage');
      return;
    }


    // Check if user is an admin
    const adminRef = doc(db, "Admins", user.uid);
    const adminSnap = await getDoc(adminRef);

    if (adminSnap.exists()) {
      showMessage('Access Denied', 'signInMessage');
      await auth.signOut();
      return;
    }


    // Get teacher data from Firestore
    const teacherRef = doc(db, "Instructors", user.uid);
    const teacherSnap = await getDoc(teacherRef);

    if (teacherSnap.exists()) {
      const teacherData = teacherSnap.data();

      // Check verification status
      if (teacherData.verificationStatus === 'pending') {
        showMessage('Your account is pending verification by an administrator.', 'signInMessage');
        // Store minimal teacher data for the pending page
        localStorage.setItem('pendingTeacherData', JSON.stringify({
          email: teacherData.email,
          firstName: teacherData.firstName,
          lastName: teacherData.lastName,
          verificationStatus: 'pending'
        }));
        // Redirect to pending verification page
        window.location.href = 'pending-verification-teacher.html';
        return;
      } else if (teacherData.verificationStatus === 'rejected') {
        showMessage('Your account verification was rejected. Please contact the school administration.', 'signInMessage');
        await auth.signOut();
        return;
      } else if (teacherData.verificationStatus !== 'approved' && teacherData.verificationStatus !== undefined) {
        // For any other status that's not approved
        showMessage('Your account has an invalid status. Please contact the school administration.', 'signInMessage');
        await auth.signOut();
        return;
      }

      // If verification status is approved or undefined (for backward compatibility with existing accounts)
      // Store teacher data in localStorage with session timestamp
      localStorage.setItem('teacherData', JSON.stringify({
        email: teacherData.email,
        firstName: teacherData.firstName,
        lastName: teacherData.lastName,
        verificationStatus: teacherData.verificationStatus || 'approved', // Default to approved for existing accounts
        teacherId: teacherData.teacherId || null, // Include teacher ID if it exists
        sessionTimestamp: Date.now() // Add session timestamp for security
      }));

      // Successful Login
      showMessage('Login Successful', 'signInMessage');
      localStorage.setItem('loggedInUserId', user.uid);

      window.location.href = 'Teacher.html';
    }

  } catch (error) {
    failedLoginAttempts++;
    if (error.code === 'auth/user-not-found') {
      showMessage('Account does not exist.', 'signInMessage');
    } else if (error.code === 'auth/wrong-password') {
      showMessage('Incorrect Email or Password', 'signInMessage');
    } else {
      showMessage('Unable to log in', 'signInMessage');
    }

    // Lockout logic
    if (failedLoginAttempts >= maxFailedAttempts) {
      signInButton.disabled = true;
      const countdownMessage = document.getElementById('countdownMessage');
      countdownMessage.style.display = 'block';

      let timeLeft = disableDuration / 1000; // Convert to seconds
      const countdownInterval = setInterval(() => {
        timeLeft--;
        countdownMessage.textContent = `Please wait ${timeLeft} seconds before trying again.`;

        if (timeLeft <= 0) {
          clearInterval(countdownInterval);
          countdownMessage.style.display = 'none';
          signInButton.disabled = false;
          failedLoginAttempts = 0; // Reset after lockout period
        }
      }, 1000);
    }
  }
});
