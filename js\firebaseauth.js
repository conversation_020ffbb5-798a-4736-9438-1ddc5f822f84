// Import Firebase Modules
import { initializeApp } from "https://www.gstatic.com/firebasejs/10.11.1/firebase-app.js";
import { getAuth, createUserWithEmailAndPassword, signInWithEmailAndPassword } from "https://www.gstatic.com/firebasejs/10.11.1/firebase-auth.js";
import { getFirestore, setDoc, doc, collection, getDoc } from "https://www.gstatic.com/firebasejs/10.11.1/firebase-firestore.js";

// Firebase Configuration
const firebaseConfig = {
  apiKey: "AIzaSyBuj8sMvbDKmjkAVG5JdVOdEF4OO9ijjzA",
  authDomain: "lead-login.firebaseapp.com",
  projectId: "lead-login",
  storageBucket: "lead-login.appspot.com",
  messagingSenderId: "1051456252675",
  appId: "1:1051456252675:web:61073e11903055f889d736"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const auth = getAuth();
const db = getFirestore(app);

// Variables for Login Attempts
let failedLoginAttempts = parseInt(localStorage.getItem("failedAttempts")) || 0;
const maxFailedAttempts = 10;
const disableDuration = 3 * 60 * 1000; // 3 minutes in milliseconds

// Show message function
function showMessage(message, divId) {
  var messageDiv = document.getElementById(divId);
  messageDiv.style.display = "block";
  messageDiv.innerHTML = message;
  messageDiv.style.opacity = 1;
  setTimeout(function () {
    messageDiv.style.opacity = 0;
  }, 5000);
}

// Signup Event Listener
const signUp = document.getElementById('submitSignUp');
signUp.addEventListener('click', async (event) => {
  event.preventDefault();
  const email = document.getElementById('rEmail').value;
  const password = document.getElementById('rPassword').value;
  const firstName = document.getElementById('fName').value;
  const lastName = document.getElementById('lName').value;

  try {
    const userCredential = await createUserWithEmailAndPassword(auth, email, password);
    const user = userCredential.user;

    const userData = {
      email: email,
      firstName: firstName,
      lastName: lastName,
      verificationStatus: 'pending', // Add verification status field
      createdAt: new Date().toISOString() // Add timestamp for sorting
    };

    showMessage('Account Created Successfully. Please wait for admin approval before logging in.', 'signUpMessage');

    // Save user data in "Students" collection
    const docRef = doc(db, "Students", user.uid);
    await setDoc(docRef, userData);

    localStorage.removeItem('showSignUp');
    document.getElementById('signin').style.display = 'none';
  } catch (error) {
    const errorCode = error.code;
    if (errorCode === 'auth/email-already-in-use') {
      showMessage('Email Address Already Exists', 'signUpMessage');
    } else {
      showMessage('Unable to create User', 'signUpMessage');
    }
  }
});

// Sign-In Event Listener
const signIn = document.getElementById('submitSignIn');
signIn.addEventListener('click', async (event) => {
  event.preventDefault();

  // Prevent sign-in if disabled
  if (signIn.disabled) {
    const countdownMessage = document.getElementById('countdownMessage');
    countdownMessage.style.display = 'block';
    return;
  }

  const email = document.getElementById('email').value;
  const password = document.getElementById('password').value;

  try {
    const userCredential = await signInWithEmailAndPassword(auth, email, password);
    const user = userCredential.user;

    // Check if user is an instructor
    const instructorRef = doc(db, "Instructors", user.uid);
    const instructorSnap = await getDoc(instructorRef);

    if (instructorSnap.exists()) {
      showMessage('Access Denied', 'signInMessage');
      await auth.signOut();
      return;
    }

    // Check if user is an admin
    const adminRef = doc(db, "Admins", user.uid);
    const adminSnap = await getDoc(adminRef);

    if (adminSnap.exists()) {
      showMessage('Access Denied', 'signInMessage');
      await auth.signOut();
      return;
    }

    // Get student data from Firestore
    const studentRef = doc(db, "Students", user.uid);
    const studentSnap = await getDoc(studentRef);

    if (studentSnap.exists()) {
      const studentData = studentSnap.data();

      // Check verification status
      if (studentData.verificationStatus === 'pending') {
        showMessage('Your account is pending verification by an administrator.', 'signInMessage');
        // Store minimal student data for the pending page
        localStorage.setItem('pendingStudentData', JSON.stringify({
          email: studentData.email,
          firstName: studentData.firstName,
          lastName: studentData.lastName,
          verificationStatus: 'pending'
        }));
        // Redirect to pending verification page
        window.location.href = 'pending-verification.html';
        return;
      } else if (studentData.verificationStatus === 'rejected') {
        showMessage('Your account verification was rejected. Please contact the school administration.', 'signInMessage');
        await auth.signOut();
        return;
      } else if (studentData.verificationStatus !== 'approved' && studentData.verificationStatus !== undefined) {
        // For any other status that's not approved
        showMessage('Your account has an invalid status. Please contact the school administration.', 'signInMessage');
        await auth.signOut();
        return;
      }

      // If verification status is approved or undefined (for backward compatibility with existing accounts)
      // Store student data in localStorage with session timestamp
      localStorage.setItem('studentData', JSON.stringify({
        email: studentData.email,
        firstName: studentData.firstName,
        lastName: studentData.lastName,
        verificationStatus: studentData.verificationStatus || 'approved', // Default to approved for existing accounts
        sessionTimestamp: Date.now() // Add session timestamp for security
      }));

      // Successful Login
      showMessage('Login Successful', 'signInMessage');
      localStorage.setItem('loggedInUserId', user.uid);
      localStorage.removeItem("failedAttempts"); // Reset failed attempts
      failedLoginAttempts = 0;

      window.location.href = 'Student11.html';
    }
  } catch (error) {
    const errorCode = error.code;
    if (errorCode === 'auth/wrong-password' || errorCode === 'auth/user-not-found') {
      showMessage('Incorrect Email or Password', 'signInMessage');
    } else {
      showMessage('Account does not Exist', 'signInMessage');
    }

    failedLoginAttempts++;
    localStorage.setItem("failedAttempts", failedLoginAttempts);

    if (failedLoginAttempts >= maxFailedAttempts) {
      signIn.disabled = true;
      const countdownMessage = document.getElementById('countdownMessage');
      countdownMessage.style.display = 'block';

      let timeLeft = disableDuration / 1000;
      const countdownInterval = setInterval(() => {
        timeLeft--;
        countdownMessage.textContent = `Please wait ${timeLeft} seconds before trying again.`;

        if (timeLeft <= 0) {
          clearInterval(countdownInterval);
          countdownMessage.style.display = 'none';
          signIn.disabled = false;
          failedLoginAttempts = 0;
          localStorage.removeItem("failedAttempts");
        }
      }, 1000);
    }
  }
});
