<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Security Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            color: #007bff;
            margin-top: 0;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .status.valid {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.invalid {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .danger {
            background-color: #dc3545;
        }
        .danger:hover {
            background-color: #c82333;
        }
        .test-links {
            margin: 20px 0;
        }
        .test-links a {
            display: inline-block;
            margin: 5px;
            padding: 10px 15px;
            background-color: #28a745;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
        .test-links a:hover {
            background-color: #218838;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔒 Security Implementation Test</h1>
        
        <div class="test-section">
            <h3>Current Session Status</h3>
            <div id="sessionStatus">Checking...</div>
        </div>

        <div class="test-section">
            <h3>Session Data in localStorage</h3>
            <div id="localStorageStatus">Checking...</div>
        </div>

        <div class="test-section">
            <h3>Test Actions</h3>
            <button onclick="clearAllSessions()" class="danger">Clear All Sessions</button>
            <button onclick="simulateStudentLogin()">Simulate Student Login</button>
            <button onclick="simulateTeacherLogin()">Simulate Teacher Login</button>
            <button onclick="simulateAdminLogin()">Simulate Admin Login</button>
            <button onclick="checkSessionSecurity()">Check Session Security</button>
        </div>

        <div class="test-section">
            <h3>Test Direct Access (Should Redirect if No Valid Session)</h3>
            <div class="test-links">
                <a href="Student11.html" target="_blank">Test Student Page Access</a>
                <a href="Teacher.html" target="_blank">Test Teacher Page Access</a>
                <a href="Admin.html" target="_blank">Test Admin Page Access</a>
            </div>
            <div class="status warning">
                <strong>Note:</strong> These links should redirect to index.html if no valid session exists.
            </div>
        </div>

        <div class="test-section">
            <h3>Test Instructions</h3>
            <ol>
                <li><strong>Test 1:</strong> Click "Clear All Sessions" then try accessing the pages above - should redirect</li>
                <li><strong>Test 2:</strong> Simulate a login, then close the browser tab and reopen - session should be cleared</li>
                <li><strong>Test 3:</strong> Simulate a login, then copy the URL and paste in a new tab - should work initially</li>
                <li><strong>Test 4:</strong> Wait 30+ minutes with no activity - session should expire</li>
                <li><strong>Test 5:</strong> Use proper logout - should clear session and redirect</li>
            </ol>
        </div>
    </div>

    <script src="js/session-security.js"></script>
    <script>
        function updateSessionStatus() {
            const statusDiv = document.getElementById('sessionStatus');
            const localStorageDiv = document.getElementById('localStorageStatus');
            
            // Check for session data
            const studentData = localStorage.getItem('studentData');
            const teacherData = localStorage.getItem('teacherData');
            const adminData = localStorage.getItem('adminData');
            
            let sessionInfo = [];
            if (studentData) {
                try {
                    const data = JSON.parse(studentData);
                    sessionInfo.push(`Student: ${data.firstName} ${data.lastName} (${data.email})`);
                } catch (e) {
                    sessionInfo.push('Student: Invalid data');
                }
            }
            if (teacherData) {
                try {
                    const data = JSON.parse(teacherData);
                    sessionInfo.push(`Teacher: ${data.firstName} ${data.lastName} (${data.email})`);
                } catch (e) {
                    sessionInfo.push('Teacher: Invalid data');
                }
            }
            if (adminData) {
                try {
                    const data = JSON.parse(adminData);
                    sessionInfo.push(`Admin: ${data.firstName} ${data.lastName} (${data.email})`);
                } catch (e) {
                    sessionInfo.push('Admin: Invalid data');
                }
            }
            
            if (sessionInfo.length > 0) {
                statusDiv.innerHTML = `<div class="status valid">Active Sessions:<br>${sessionInfo.join('<br>')}</div>`;
            } else {
                statusDiv.innerHTML = '<div class="status invalid">No active sessions found</div>';
            }
            
            // Show localStorage contents
            const allKeys = Object.keys(localStorage);
            const relevantKeys = allKeys.filter(key => 
                key.includes('Data') || key.includes('loggedIn') || key.includes('session')
            );
            
            if (relevantKeys.length > 0) {
                localStorageDiv.innerHTML = `<div class="status warning">localStorage keys: ${relevantKeys.join(', ')}</div>`;
            } else {
                localStorageDiv.innerHTML = '<div class="status valid">No session-related data in localStorage</div>';
            }
        }

        function clearAllSessions() {
            localStorage.removeItem('studentData');
            localStorage.removeItem('teacherData');
            localStorage.removeItem('adminData');
            localStorage.removeItem('loggedInUserId');
            localStorage.removeItem('enrolledClassrooms');
            localStorage.removeItem('teacherCards');
            alert('All sessions cleared!');
            updateSessionStatus();
        }

        function simulateStudentLogin() {
            const studentData = {
                email: '<EMAIL>',
                firstName: 'Test',
                lastName: 'Student',
                verificationStatus: 'approved',
                sessionTimestamp: Date.now()
            };
            localStorage.setItem('studentData', JSON.stringify(studentData));
            localStorage.setItem('loggedInUserId', 'test-student-uid');
            alert('Student session simulated!');
            updateSessionStatus();
        }

        function simulateTeacherLogin() {
            const teacherData = {
                email: '<EMAIL>',
                firstName: 'Test',
                lastName: 'Teacher',
                verificationStatus: 'approved',
                teacherId: 'TEACH001',
                sessionTimestamp: Date.now()
            };
            localStorage.setItem('teacherData', JSON.stringify(teacherData));
            localStorage.setItem('loggedInUserId', 'test-teacher-uid');
            alert('Teacher session simulated!');
            updateSessionStatus();
        }

        function simulateAdminLogin() {
            const adminData = {
                email: '<EMAIL>',
                firstName: 'Test',
                lastName: 'Admin',
                sessionTimestamp: Date.now()
            };
            localStorage.setItem('adminData', JSON.stringify(adminData));
            localStorage.setItem('loggedInUserId', 'test-admin-uid');
            alert('Admin session simulated!');
            updateSessionStatus();
        }

        function checkSessionSecurity() {
            if (window.SessionSecurity) {
                const security = new window.SessionSecurity();
                
                const studentValid = security.validateSession('student');
                const teacherValid = security.validateSession('teacher');
                const adminValid = security.validateSession('admin');
                
                alert(`Session Validation Results:\nStudent: ${studentValid}\nTeacher: ${teacherValid}\nAdmin: ${adminValid}`);
            } else {
                alert('SessionSecurity class not available!');
            }
        }

        // Update status on page load and every 5 seconds
        updateSessionStatus();
        setInterval(updateSessionStatus, 5000);
    </script>
</body>
</html>
